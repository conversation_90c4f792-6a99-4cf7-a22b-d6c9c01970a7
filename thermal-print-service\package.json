{"name": "fix-fogoes-thermal-print-service", "version": "1.0.0", "description": "Serviço Node.js para impressão térmica Fix Fogões", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "install-service": "node install-service.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "escpos": "^3.0.0-alpha.6", "escpos-usb": "^3.0.0-alpha.4", "escpos-serialport": "^3.0.0-alpha.4", "escpos-bluetooth": "^3.0.0-alpha.4", "node-bluetooth": "^1.2.1", "serialport": "^12.0.0", "qrcode": "^1.5.3", "jimp": "^0.22.10", "node-windows": "^1.0.0-beta.8"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["thermal-printer", "escpos", "bluetooth", "fix-fogoes"], "author": "<PERSON><PERSON>", "license": "MIT"}