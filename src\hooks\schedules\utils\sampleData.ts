
import { AgendamentoAI } from '@/services/agendamentos';

// Dados de exemplo para quando não houver agendamentos
export const sampleAgendamentos: AgendamentoAI[] = [
  {
    id: "1",
    nome: "<PERSON>",
    endereco: "Av. <PERSON>, 1000, São Paulo",
    equipamento: "Gelade<PERSON> Brastemp",
    problema: "Não gela corretamente",
    urgente: true,
    status: "pendente",
    tecnico: "<PERSON>",
    created_at: new Date().toISOString(),
    data_agendada: new Date(Date.now() + 172800000).toISOString() // dois dias no futuro
  },
  {
    id: "2",
    nome: "<PERSON> Oliveira",
    endereco: "Rua Augusta, 500, São Paulo",
    equipamento: "Máquina de lavar Electrolux",
    problema: "Vazamento de água",
    urgente: false,
    status: "pendente",
    tecnico: null,
    created_at: new Date(Date.now() - 86400000).toISOString(), // ontem
    data_agendada: null
  },
  {
    id: "3",
    nome: "<PERSON>",
    endereco: "<PERSON><PERSON>, 300, São Paulo",
    equipamento: "Fogão Continental",
    problema: "Não acende o forno",
    urgente: false,
    status: "confirmado",
    tecnico: "Ana Costa",
    created_at: new Date(Date.now() - 172800000).toISOString(), // dois dias atrás
    data_agendada: new Date(Date.now() + 86400000).toISOString() // um dia no futuro
  }
];
