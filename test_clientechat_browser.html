<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Teste ClienteChat Duas Etapas</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #E5B034;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #d4a02a;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success { border-color: #28a745; background: #d4edda; }
        .error { border-color: #dc3545; background: #f8d7da; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Teste Sistema ClienteChat Duas Etapas</h1>
        <p>Testa a nova lógica que detecta automaticamente primeira consulta vs escolha de horário</p>

        <div class="test-section">
            <h3>🎯 ETAPA 1: Primeira Consulta (Dados Completos)</h3>
            <button onclick="testPrimeiraConsulta()">Testar Primeira Consulta</button>
            <div id="resultado1" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>🎯 ETAPA 2: Escolha de Horário (Número 1, 2 ou 3)</h3>
            <button onclick="testEscolhaHorario('1')">Escolher Opção 1</button>
            <button onclick="testEscolhaHorario('2')">Escolher Opção 2</button>
            <button onclick="testEscolhaHorario('3')">Escolher Opção 3</button>
            <div id="resultado2" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>❌ TESTE: Mensagem Incompleta</h3>
            <button onclick="testMensagemIncompleta()">Testar Mensagem Incompleta</button>
            <div id="resultado3" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        const API_URL = 'https://fix-agendamento-production.up.railway.app/agendamento-inteligente';

        async function fazerRequisicao(dados, resultadoId) {
            const resultadoDiv = document.getElementById(resultadoId);
            resultadoDiv.style.display = 'block';
            resultadoDiv.textContent = '⏳ Enviando requisição...';
            resultadoDiv.className = 'result';

            try {
                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(dados)
                });

                const resultado = await response.json();
                
                resultadoDiv.textContent = `📡 Status: ${response.status}\n📝 Resposta:\n${JSON.stringify(resultado, null, 2)}`;
                resultadoDiv.className = response.ok ? 'result success' : 'result error';
                
                return resultado;
            } catch (error) {
                resultadoDiv.textContent = `❌ Erro: ${error.message}`;
                resultadoDiv.className = 'result error';
                return null;
            }
        }

        async function testPrimeiraConsulta() {
            const dados = {
                message: `Nome: João Silva
Endereço: Rua das Flores, 123, Centro, Florianópolis, SC, CEP: 88010-000
CPF: 123.456.789-00
E-mail: <EMAIL>
Equipamento: Fogão`,
                phone: "48988332664"
            };

            await fazerRequisicao(dados, 'resultado1');
        }

        async function testEscolhaHorario(opcao) {
            const dados = {
                message: opcao,
                phone: "48988332664"
            };

            await fazerRequisicao(dados, 'resultado2');
        }

        async function testMensagemIncompleta() {
            const dados = {
                message: "Quero agendar um serviço",
                phone: "48988332664"
            };

            await fazerRequisicao(dados, 'resultado3');
        }
    </script>
</body>
</html>
